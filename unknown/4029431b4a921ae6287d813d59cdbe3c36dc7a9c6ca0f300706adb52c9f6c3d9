"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { FileText, Edit, Trash2, Plus, Eye, RefreshCw } from "lucide-react";
import { toast } from "sonner";

interface Template {
  id: string;
  name: string;
  description: string;
  filename: string;
  placeholders: string[];
  layoutSize: "A4" | "Letter";
  uploadedAt: string;
}

export default function ManageTemplatesPage() {
  const router = useRouter();
  const [templates, setTemplates] = useState<Template[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [reformattingId, setReformattingId] = useState<string | null>(null);

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      const response = await fetch("/api/templates");
      if (!response.ok) {
        throw new Error("Failed to load templates");
      }
      const data = await response.json();
      setTemplates(data);
    } catch (error) {
      console.error("Error loading templates:", error);
      toast.error("Failed to load templates");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: string, name: string) => {
    if (
      !confirm(
        `Are you sure you want to delete "${name}"? This will also delete any associated image folders. This action cannot be undone.`
      )
    ) {
      return;
    }

    setDeletingId(id);
    try {
      const response = await fetch(`/api/templates/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete template");
      }

      setTemplates((prev) => prev.filter((t) => t.id !== id));
      toast.success("Template deleted successfully");
    } catch (error) {
      console.error("Error deleting template:", error);
      toast.error("Failed to delete template");
    } finally {
      setDeletingId(null);
    }
  };

  const handlePreview = (template: Template) => {
    // Open template preview in a new window
    window.open(`/templates/${template.filename}`, "_blank");
  };

  const handleReformat = async (id: string, name: string) => {
    if (
      !confirm(
        `Reformat "${name}"? This will clean up Word formatting and may change the appearance.`
      )
    ) {
      return;
    }

    setReformattingId(id);
    try {
      const response = await fetch("/api/templates/reformat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ templateId: id }),
      });

      if (!response.ok) {
        throw new Error("Failed to reformat template");
      }

      const result = await response.json();

      // Update the template in state with new placeholders
      setTemplates((prev) =>
        prev.map((t) =>
          t.id === id ? { ...t, placeholders: result.placeholders } : t
        )
      );

      toast.success("Template reformatted successfully");
    } catch (error) {
      console.error("Error reformatting template:", error);
      toast.error("Failed to reformat template");
    } finally {
      setReformattingId(null);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading templates...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-semibold text-foreground mb-2">
            Manage Templates
          </h1>
          <p className="text-muted-foreground">
            View, edit, and manage your document templates.
          </p>
        </div>
        <Button onClick={() => router.push("/templates/add")}>
          <Plus className="h-4 w-4" />
          Add Template
        </Button>
      </div>

      {templates.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No templates found</h3>
            <p className="text-muted-foreground text-center mb-6">
              Get started by adding your first template.
            </p>
            <Button onClick={() => router.push("/templates/add")}>
              <Plus className="h-4 w-4" />
              Add Your First Template
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templates.map((template) => (
            <Card key={template.id} className="relative">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  {template.name}
                </CardTitle>
                <CardDescription>{template.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-1">
                      Placeholders ({template.placeholders.length})
                    </p>
                    <div className="flex flex-wrap gap-1">
                      {template.placeholders.slice(0, 3).map((placeholder) => (
                        <span
                          key={placeholder}
                          className="inline-flex items-center px-2 py-1 rounded-md bg-primary/10 text-primary text-xs font-medium"
                        >
                          {placeholder}
                        </span>
                      ))}
                      {template.placeholders.length > 3 && (
                        <span className="inline-flex items-center px-2 py-1 rounded-md bg-muted text-muted-foreground text-xs">
                          +{template.placeholders.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-1">
                        Layout Size
                      </p>
                      <span className="inline-flex items-center px-2 py-1 rounded-md bg-secondary text-secondary-foreground text-xs font-medium">
                        {template.layoutSize}
                      </span>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-muted-foreground">
                        Added{" "}
                        {new Date(template.uploadedAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>

                  <div className="flex gap-2 pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePreview(template)}
                      className="flex-1"
                    >
                      <Eye className="h-4 w-4" />
                      Preview
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleReformat(template.id, template.name)}
                      disabled={reformattingId === template.id}
                      title="Clean up Word formatting"
                    >
                      <RefreshCw
                        className={`h-4 w-4 ${
                          reformattingId === template.id ? "animate-spin" : ""
                        }`}
                      />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        router.push(`/templates/edit/${template.id}`)
                      }
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(template.id, template.name)}
                      disabled={deletingId === template.id}
                      className="text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
